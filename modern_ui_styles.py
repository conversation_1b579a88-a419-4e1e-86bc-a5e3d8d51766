# -*- coding: utf-8 -*-
"""
现代化UI样式定义
包含科技感浅色主题、Neumorphism效果、渐变与光效等现代设计元素
"""

class ModernUIStyles:
    """现代化UI样式类"""
    
    # 色彩方案
    COLORS = {
        'primary': '#667eea',      # 科技蓝
        'secondary': '#764ba2',    # 深紫
        'background': '#f8fafc',   # 浅灰白
        'surface': '#ffffff',      # 纯白
        'text_primary': '#1e293b', # 深灰
        'text_secondary': '#64748b', # 中灰
        'success': '#10b981',      # 翠绿
        'warning': '#f59e0b',      # 橙黄
        'error': '#ef4444',        # 红色
        'info': '#3b82f6',         # 蓝色
        'border': '#e2e8f0',       # 边框色
        'shadow': 'rgba(0, 0, 0, 0.1)', # 阴影色
        'glass': 'rgba(255, 255, 255, 0.25)', # 玻璃效果
    }
    
    @staticmethod
    def get_main_window_style():
        """获取主窗口样式"""
        return f"""
        QWidget {{
            background: {ModernUIStyles.COLORS['background']};
            color: {ModernUIStyles.COLORS['text_primary']};
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            font-size: 11pt;
        }}
        
        QMainWindow {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['background']} 0%, #e2e8f0 100%);
        }}
        """
    
    @staticmethod
    def get_modern_card_style():
        """获取现代化卡片样式"""
        return f"""
        QFrame {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-radius: 16px;
            padding: 20px;
            margin: 10px;
        }}
        
        QGroupBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 12px;
            padding: 15px;
            margin: 8px;
            font-weight: 600;
            font-size: 12pt;
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background: {ModernUIStyles.COLORS['surface']};
            color: {ModernUIStyles.COLORS['primary']};
        }}
        """
    
    @staticmethod
    def get_modern_button_style():
        """获取现代化按钮样式"""
        return f"""
        QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            transform: translateY(0px);
        }}
        
        QPushButton:disabled {{
            background: #9ca3af;
            color: #6b7280;
        }}
        """
    
    @staticmethod
    def get_success_button_style():
        """获取成功按钮样式"""
        return f"""
        QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            color: white;
            border: 2px solid #059669;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            border-color: #047857;
            transform: translateY(-2px);
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            border-color: #065f46;
            transform: translateY(0px);
        }}
        
        QPushButton:disabled {{
            background: #9ca3af;
            color: #6b7280;
            border-color: #9ca3af;
        }}
        """
    
    @staticmethod
    def get_modern_input_style():
        """获取现代化输入框样式"""
        return f"""
        QLineEdit, QSpinBox, QDoubleSpinBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 11pt;
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {ModernUIStyles.COLORS['primary']};
            background: {ModernUIStyles.COLORS['surface']};
        }}
        
        QLineEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover {{
            border-color: #cbd5e1;
        }}
        """
    
    @staticmethod
    def get_modern_combobox_style():
        """获取现代化下拉框样式"""
        return f"""
        QComboBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 11pt;
            color: {ModernUIStyles.COLORS['text_primary']};
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernUIStyles.COLORS['primary']};
        }}
        
        QComboBox:hover {{
            border-color: #cbd5e1;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernUIStyles.COLORS['text_secondary']};
            margin-right: 10px;
        }}
        
        QComboBox QAbstractItemView {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-radius: 8px;
            selection-background-color: {ModernUIStyles.COLORS['primary']};
            selection-color: white;
            padding: 5px;
        }}
        """
    
    @staticmethod
    def get_modern_checkbox_style():
        """获取现代化复选框样式"""
        return f"""
        QCheckBox {{
            font-weight: 600;
            color: {ModernUIStyles.COLORS['text_primary']};
            spacing: 10px;
            font-size: 11pt;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 10px;
            border: 2px solid {ModernUIStyles.COLORS['border']};
            background: {ModernUIStyles.COLORS['surface']};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {ModernUIStyles.COLORS['primary']};
        }}
        
        QCheckBox::indicator:checked {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            border-color: {ModernUIStyles.COLORS['success']};
        }}
        """
    
    @staticmethod
    def get_modern_slider_style():
        """获取现代化滑块样式"""
        return f"""
        QSlider::groove:horizontal {{
            border: 1px solid {ModernUIStyles.COLORS['border']};
            height: 8px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 4px;
        }}
        
        QSlider::handle:horizontal {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border: 2px solid {ModernUIStyles.COLORS['primary']};
            width: 20px;
            margin: -6px 0;
            border-radius: 10px;
        }}
        
        QSlider::handle:horizontal:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            border-color: #5a67d8;
        }}
        """
    
    @staticmethod
    def get_modern_tab_style():
        """获取现代化Tab样式"""
        return f"""
        QTabWidget::pane {{
            border: none;
            background: {ModernUIStyles.COLORS['surface']};
            border-radius: 16px;
            margin-top: 10px;
        }}
        
        QTabBar::tab {{
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: {ModernUIStyles.COLORS['text_secondary']};
            padding: 12px 20px;
            margin-right: 4px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-bottom: none;
            font-weight: 600;
            min-width: 80px;
        }}
        
        QTabBar::tab:selected {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border-color: {ModernUIStyles.COLORS['primary']};
        }}
        
        QTabBar::tab:hover:!selected {{
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        }}
        """
    
    @staticmethod
    def get_status_label_style(status="disconnected"):
        """获取状态标签样式"""
        if status == "connected":
            bg_color = ModernUIStyles.COLORS['success']
            text_color = "white"
            icon = "✅"
        else:
            bg_color = ModernUIStyles.COLORS['error']
            text_color = "white"
            icon = "❌"
            
        return f"""
        QLabel {{
            background: {bg_color};
            color: {text_color};
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 600;
            font-size: 11pt;
        }}
        """
    
    @staticmethod
    def get_title_style():
        """获取标题样式"""
        return f"""
        QLabel {{
            font-size: 18pt;
            font-weight: 700;
            color: {ModernUIStyles.COLORS['primary']};
            padding: 15px;
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['surface']} 0%, #f1f5f9 100%);
            border-radius: 12px;
            border: 2px solid {ModernUIStyles.COLORS['border']};
        }}
        """
