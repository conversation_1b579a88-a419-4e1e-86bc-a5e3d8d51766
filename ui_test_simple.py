#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTabWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

class SimpleTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🎬 OBS 智能去重软件 v1.3 - 现代化版本')
        self.setGeometry(200, 50, 1200, 900)
        self.setMinimumSize(1200, 900)
        
        # 应用现代化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                color: #1e293b;
                font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
                font-size: 11pt;
            }
            
            QTabWidget::pane {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 15px;
                margin-top: 10px;
            }
            QTabBar::tab {
                background-color: #f1f5f9;
                color: #64748b;
                padding: 12px 20px;
                margin-right: 4px;
                margin-top: 4px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 120px;
                border: 2px solid #e2e8f0;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #667eea;
                color: white;
                border-color: #667eea;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e2e8f0;
                color: #475569;
            }
            
            QPushButton {
                background-color: #667eea;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a67d8;
            }
            QPushButton:pressed {
                background-color: #4c51bf;
            }
            
            QLabel {
                color: #1e293b;
                background-color: transparent;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # 标题
        title = QLabel("🎬 现代化UI升级测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 20px;
                background-color: white;
                border-radius: 15px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 创建Tab控件
        tab_widget = QTabWidget()
        
        # Tab 1
        tab1 = QWidget()
        tab1_layout = QVBoxLayout(tab1)
        tab1_layout.addWidget(QLabel("这是视频去重功能页面"))
        test_btn = QPushButton("测试按钮")
        tab1_layout.addWidget(test_btn)
        tab_widget.addTab(tab1, "🎬 视频去重")
        
        # Tab 2
        tab2 = QWidget()
        tab2_layout = QVBoxLayout(tab2)
        tab2_layout.addWidget(QLabel("这是音频去重功能页面"))
        tab_widget.addTab(tab2, "🎵 音频去重")
        
        layout.addWidget(tab_widget)
        self.setLayout(layout)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置应用图标
    icon_path = os.path.join(os.path.dirname(__file__), 'obs2.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    window = SimpleTestWindow()
    window.show()
    
    sys.exit(app.exec_())
