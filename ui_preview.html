<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 OBS 智能去重控制器 - 现代化UI预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .sidebar {
            width: 300px;
            background: #f8fafc;
            border-right: 2px solid #e2e8f0;
            padding: 20px;
        }

        .content-area {
            flex: 1;
            padding: 20px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 
                8px 8px 16px rgba(163, 177, 198, 0.6),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 
                12px 12px 24px rgba(163, 177, 198, 0.8),
                -12px -12px 24px rgba(255, 255, 255, 0.9);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .modern-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 4px 15px rgba(102, 126, 234, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            margin: 5px;
        }

        .modern-button:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 
                0 8px 25px rgba(102, 126, 234, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .modern-button.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .modern-button.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .modern-button.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .modern-input {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 11pt;
            color: #1e293b;
            width: 100%;
            margin: 5px 0;
            transition: all 0.2s ease;
        }

        .modern-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modern-checkbox {
            display: flex;
            align-items: center;
            margin: 10px 0;
            cursor: pointer;
        }

        .modern-checkbox input {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            accent-color: #10b981;
        }

        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 11pt;
            margin: 5px;
        }

        .status-connected {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .status-disconnected {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px 12px 0 0;
            margin-bottom: 0;
        }

        .tab {
            padding: 12px 20px;
            background: #f1f5f9;
            color: #64748b;
            border: none;
            cursor: pointer;
            font-weight: 600;
            border-radius: 12px 12px 0 0;
            margin-right: 4px;
            transition: all 0.2s ease;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 -2px 10px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            background: white;
            border-radius: 0 16px 16px 16px;
            padding: 20px;
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.1),
                0 1px 8px rgba(0, 0, 0, 0.2);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 
                8px 8px 16px rgba(163, 177, 198, 0.6),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                12px 12px 24px rgba(163, 177, 198, 0.8),
                -12px -12px 24px rgba(255, 255, 255, 0.9);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .feature-desc {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .glass-morphism {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 16px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #64748b;
            border-top: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 OBS 智能去重控制器</h1>
            <p>现代化科技感界面 · 智能化去重方案</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="card">
                    <div class="card-title">⚙️ 智能配置面板</div>
                    
                    <label>视频媒体源:</label>
                    <select class="modern-input">
                        <option>摄像头</option>
                        <option>屏幕捕获</option>
                        <option>视频文件</option>
                    </select>

                    <label>音频媒体源:</label>
                    <select class="modern-input">
                        <option>麦克风</option>
                        <option>系统音频</option>
                        <option>音频文件</option>
                    </select>

                    <button class="modern-button">🔄 刷新媒体源</button>
                </div>

                <div class="card">
                    <div class="card-title">⚡ 快速设置</div>
                    
                    <label>预设方案:</label>
                    <select class="modern-input">
                        <option>🎯 轻度去重</option>
                        <option>🔥 中度去重</option>
                        <option>💪 强力去重</option>
                        <option>🚀 极限去重</option>
                    </select>

                    <button class="modern-button warning">✨ 应用预设</button>
                </div>

                <div class="card">
                    <div class="card-title">📊 系统状态</div>
                    <p>CPU 使用率: 25%</p>
                    <p>内存使用率: 60%</p>
                    <p>活跃功能: 3</p>
                </div>
            </div>

            <div class="content-area">
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>🎨 现代化UI控制面板</h2>
                        <div>
                            <span class="status-indicator status-connected pulse">🟢 已连接</span>
                            <button class="modern-button">🔗 连接到 OBS</button>
                            <button class="modern-button success">🚀 一键启动</button>
                        </div>
                    </div>
                </div>

                <div class="tabs">
                    <button class="tab active" onclick="showTab('video')">🎬 视频去重</button>
                    <button class="tab" onclick="showTab('audio')">🎵 音频去重</button>
                    <button class="tab" onclick="showTab('advanced')">⚡ 高级功能</button>
                    <button class="tab" onclick="showTab('help')">📖 说明文档</button>
                </div>

                <div class="tab-content">
                    <div id="video-tab">
                        <h3>🎬 视频去重功能</h3>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <div class="feature-icon">🚀</div>
                                <div class="feature-title">智能加减速</div>
                                <div class="feature-desc">动态调整播放速度避免重复检测</div>
                                <label class="modern-checkbox">
                                    <input type="checkbox" checked>
                                    启用智能加减速
                                </label>
                                <button class="modern-button">配置参数</button>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">🌫️</div>
                                <div class="feature-title">模糊去重</div>
                                <div class="feature-desc">实时调整视频模糊度改变特征</div>
                                <label class="modern-checkbox">
                                    <input type="checkbox">
                                    启用模糊去重
                                </label>
                                <button class="modern-button">配置参数</button>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">↔️</div>
                                <div class="feature-title">移动去重</div>
                                <div class="feature-desc">随机移动视频位置避免重复</div>
                                <label class="modern-checkbox">
                                    <input type="checkbox">
                                    启用移动去重
                                </label>
                                <button class="modern-button">配置参数</button>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">🎨</div>
                                <div class="feature-title">颜色去重</div>
                                <div class="feature-desc">调整颜色参数改变视觉效果</div>
                                <label class="modern-checkbox">
                                    <input type="checkbox">
                                    启用颜色去重
                                </label>
                                <button class="modern-button">配置参数</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 现代化UI升级版 - 让OBS控制更加优雅</p>
            <p>🎨 科技感浅色主题 • ✨ Neumorphism新拟态设计 • 🌟 渐变与光效 • 💎 玻璃拟态效果</p>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 简单的标签页切换演示
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以添加实际的标签页内容切换逻辑
            console.log('切换到标签页:', tabName);
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为按钮添加点击效果
            const buttons = document.querySelectorAll('.modern-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'translateY(0px)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);
                });
            });

            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.card, .feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        });
    </script>
</body>
</html>
