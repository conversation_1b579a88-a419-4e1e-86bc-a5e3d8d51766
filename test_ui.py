#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('🎬 OBS 智能去重软件 v1.3 - 现代化版本')
        self.setGeometry(200, 50, 800, 600)
        
        # 设置现代化样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                color: #1e293b;
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
                font-size: 11pt;
            }
            
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 rgba(255,255,255,0.9), stop:1 rgba(248,250,252,0.8));
                color: #667eea;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 18px;
                padding: 20px;
                font-size: 18pt;
                font-weight: 700;
                backdrop-filter: blur(10px);
                box-shadow: 0 6px 25px rgba(102, 126, 234, 0.15);
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 12px 25px;
                font-weight: 700;
                font-size: 12pt;
                min-width: 140px;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #5a67d8, stop:1 #6b46c1);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title = QLabel("🎬 现代化UI升级完成！")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试按钮
        test_btn = QPushButton("✨ 测试按钮")
        layout.addWidget(test_btn)
        
        self.setLayout(layout)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置应用图标
    icon_path = os.path.join(os.path.dirname(__file__), 'obs2.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())
