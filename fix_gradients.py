#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def fix_gradients():
    """修复文件中的qlineargradient语法"""
    
    # 读取文件
    with open('main_module.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换规则
    replacements = [
        # 基本渐变替换为纯色
        (r'background:\s*qlineargradient\([^)]+stop:0\s*#667eea[^)]+\);', 'background-color: #667eea;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*#10b981[^)]+\);', 'background-color: #10b981;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*#ef4444[^)]+\);', 'background-color: #ef4444;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*#f8fafc[^)]+\);', 'background-color: #f8fafc;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*rgba\(255,255,255[^)]+\);', 'background-color: white;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*rgba\(248,250,252[^)]+\);', 'background-color: #f8fafc;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*rgba\(241,\s*245,\s*249[^)]+\);', 'background-color: #f1f5f9;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*rgba\(226,\s*232,\s*240[^)]+\);', 'background-color: #e2e8f0;'),
        (r'background:\s*qlineargradient\([^)]+stop:0\s*#cbd5e1[^)]+\);', 'background-color: #cbd5e1;'),
        
        # selection-background-color
        (r'selection-background-color:\s*qlineargradient\([^)]+stop:0\s*#667eea[^)]+\);', 'selection-background-color: #667eea;'),
        
        # 多行qlineargradient处理
        (r'qlineargradient\([^)]+\)', lambda m: 'white' if 'rgba(255,255,255' in m.group() else '#667eea' if '#667eea' in m.group() else '#10b981' if '#10b981' in m.group() else '#f8fafc'),
    ]
    
    # 应用替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 特殊处理：移除不支持的CSS属性
    unsupported_props = [
        r'backdrop-filter:[^;]+;',
        r'box-shadow:[^;]+;',
        r'transform:[^;]+;',
    ]
    
    for prop in unsupported_props:
        content = re.sub(prop, '', content, flags=re.MULTILINE)
    
    # 写回文件
    with open('main_module.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复所有qlineargradient语法问题")

if __name__ == '__main__':
    fix_gradients()
