# -*- coding: utf-8 -*-
"""
现代化UI演示脚本
展示现代化UI组件的效果，无需依赖原始OBS模块
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QScrollArea, QTabWidget
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

# 导入现代化UI组件
from modern_ui_styles import ModernUIStyles
from modern_ui_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernCheckBox, ModernSlider, StatusIndicator, ModernProgressBar
)

class ModernUIDemo(QWidget):
    """现代化UI演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_ui()
        self.setup_demo_data()
    
    def setup_window(self):
        """设置窗口"""
        self.setWindowTitle('🎨 现代化UI组件演示')
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(800, 600)
        
        # 应用现代化样式
        self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_ui(self):
        """创建用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 标题
        title = QLabel("🎨 现代化UI组件演示")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 24pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                padding: 20px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 16px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
            }}
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # 创建标签页
        self.create_tabs(main_layout)
    
    def create_tabs(self, parent_layout):
        """创建演示标签页"""
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(ModernUIStyles.get_modern_tab_style())
        
        # 按钮演示
        self.create_button_demo_tab(tab_widget)
        
        # 输入控件演示
        self.create_input_demo_tab(tab_widget)
        
        # 卡片演示
        self.create_card_demo_tab(tab_widget)
        
        # 状态指示器演示
        self.create_status_demo_tab(tab_widget)
        
        parent_layout.addWidget(tab_widget)
    
    def create_button_demo_tab(self, tab_widget):
        """创建按钮演示标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # 不同类型的按钮
        buttons_card = ModernCard("🔘 现代化按钮演示")
        buttons_layout = QVBoxLayout()
        
        # 主要按钮
        primary_layout = QHBoxLayout()
        primary_btn = ModernButton("主要按钮", "primary")
        success_btn = ModernButton("成功按钮", "success")
        warning_btn = ModernButton("警告按钮", "warning")
        error_btn = ModernButton("错误按钮", "error")
        
        primary_layout.addWidget(primary_btn)
        primary_layout.addWidget(success_btn)
        primary_layout.addWidget(warning_btn)
        primary_layout.addWidget(error_btn)
        buttons_layout.addLayout(primary_layout)
        
        # 功能按钮
        function_layout = QHBoxLayout()
        connect_btn = ModernButton("🔗 连接", "primary")
        start_btn = ModernButton("🚀 启动", "success")
        pause_btn = ModernButton("⏸️ 暂停", "warning")
        stop_btn = ModernButton("⏹️ 停止", "error")
        
        function_layout.addWidget(connect_btn)
        function_layout.addWidget(start_btn)
        function_layout.addWidget(pause_btn)
        function_layout.addWidget(stop_btn)
        buttons_layout.addLayout(function_layout)
        
        buttons_card.add_widget(QWidget())
        buttons_card.layout().addLayout(buttons_layout)
        layout.addWidget(buttons_card)
        
        layout.addStretch()
        tab_widget.addTab(widget, "🔘 按钮")
    
    def create_input_demo_tab(self, tab_widget):
        """创建输入控件演示标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # 输入控件卡片
        input_card = ModernCard("📝 输入控件演示")
        input_layout = QVBoxLayout()
        
        # 文本输入框
        text_input = ModernInput("请输入文本...")
        input_layout.addWidget(QLabel("文本输入框:"))
        input_layout.addWidget(text_input)
        
        # 下拉框
        combo_box = ModernComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3", "选项4"])
        input_layout.addWidget(QLabel("下拉选择框:"))
        input_layout.addWidget(combo_box)
        
        # 复选框
        checkbox_layout = QHBoxLayout()
        checkbox1 = ModernCheckBox("启用功能1")
        checkbox2 = ModernCheckBox("启用功能2")
        checkbox3 = ModernCheckBox("启用功能3")
        checkbox_layout.addWidget(checkbox1)
        checkbox_layout.addWidget(checkbox2)
        checkbox_layout.addWidget(checkbox3)
        input_layout.addWidget(QLabel("复选框:"))
        input_layout.addLayout(checkbox_layout)
        
        # 滑块
        slider = ModernSlider()
        slider.setRange(0, 100)
        slider.setValue(50)
        input_layout.addWidget(QLabel("滑块控件:"))
        input_layout.addWidget(slider)
        
        input_card.add_widget(QWidget())
        input_card.layout().addLayout(input_layout)
        layout.addWidget(input_card)
        
        layout.addStretch()
        tab_widget.addTab(widget, "📝 输入控件")
    
    def create_card_demo_tab(self, tab_widget):
        """创建卡片演示标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # 卡片网格布局
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(20)
        
        # 功能卡片1
        card1 = ModernCard("🎬 视频处理")
        card1_layout = QVBoxLayout()
        card1_layout.addWidget(QLabel("智能视频去重功能"))
        card1_layout.addWidget(ModernButton("启动处理", "primary"))
        card1.add_widget(QWidget())
        card1.layout().addLayout(card1_layout)
        cards_layout.addWidget(card1)
        
        # 功能卡片2
        card2 = ModernCard("🎵 音频处理")
        card2_layout = QVBoxLayout()
        card2_layout.addWidget(QLabel("智能音频去重功能"))
        card2_layout.addWidget(ModernButton("启动处理", "success"))
        card2.add_widget(QWidget())
        card2.layout().addLayout(card2_layout)
        cards_layout.addWidget(card2)
        
        # 功能卡片3
        card3 = ModernCard("⚙️ 系统设置")
        card3_layout = QVBoxLayout()
        card3_layout.addWidget(QLabel("系统配置和管理"))
        card3_layout.addWidget(ModernButton("打开设置", "warning"))
        card3.add_widget(QWidget())
        card3.layout().addLayout(card3_layout)
        cards_layout.addWidget(card3)
        
        layout.addLayout(cards_layout)
        
        # 第二行卡片
        cards_layout2 = QHBoxLayout()
        cards_layout2.setSpacing(20)
        
        # 统计卡片
        stats_card = ModernCard("📊 实时统计")
        stats_layout = QVBoxLayout()
        stats_layout.addWidget(QLabel("CPU使用率: 25%"))
        stats_layout.addWidget(QLabel("内存使用率: 60%"))
        stats_layout.addWidget(QLabel("活跃功能: 3"))
        stats_card.add_widget(QWidget())
        stats_card.layout().addLayout(stats_layout)
        cards_layout2.addWidget(stats_card)
        
        # 进度卡片
        progress_card = ModernCard("📈 处理进度")
        progress_layout = QVBoxLayout()
        progress_bar = ModernProgressBar()
        progress_bar.setValue(75)
        progress_layout.addWidget(QLabel("当前任务进度:"))
        progress_layout.addWidget(progress_bar)
        progress_card.add_widget(QWidget())
        progress_card.layout().addLayout(progress_layout)
        cards_layout2.addWidget(progress_card)
        
        layout.addLayout(cards_layout2)
        layout.addStretch()
        
        tab_widget.addTab(widget, "🎴 卡片")
    
    def create_status_demo_tab(self, tab_widget):
        """创建状态指示器演示标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # 状态指示器卡片
        status_card = ModernCard("🚦 状态指示器")
        status_layout = QVBoxLayout()
        
        # 连接状态
        self.status_connected = StatusIndicator("connected")
        self.status_disconnected = StatusIndicator("disconnected")
        
        status_layout.addWidget(QLabel("连接状态演示:"))
        status_layout.addWidget(self.status_connected)
        status_layout.addWidget(self.status_disconnected)
        
        # 切换按钮
        toggle_btn = ModernButton("切换状态", "primary")
        toggle_btn.clicked.connect(self.toggle_status)
        status_layout.addWidget(toggle_btn)
        
        status_card.add_widget(QWidget())
        status_card.layout().addLayout(status_layout)
        layout.addWidget(status_card)
        
        # 动画演示卡片
        animation_card = ModernCard("✨ 动画效果")
        animation_layout = QVBoxLayout()
        
        animation_layout.addWidget(QLabel("悬停按钮查看动画效果:"))
        
        animation_buttons_layout = QHBoxLayout()
        for i in range(4):
            btn = ModernButton(f"动画按钮 {i+1}", ["primary", "success", "warning", "error"][i])
            animation_buttons_layout.addWidget(btn)
        
        animation_layout.addLayout(animation_buttons_layout)
        
        animation_card.add_widget(QWidget())
        animation_card.layout().addLayout(animation_layout)
        layout.addWidget(animation_card)
        
        layout.addStretch()
        tab_widget.addTab(widget, "🚦 状态")
    
    def setup_demo_data(self):
        """设置演示数据"""
        # 设置定时器来模拟动态效果
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.update_demo_data)
        self.demo_timer.start(2000)  # 每2秒更新一次
        
        self.status_toggle = True
    
    def update_demo_data(self):
        """更新演示数据"""
        # 这里可以添加动态更新逻辑
        pass
    
    def toggle_status(self):
        """切换状态指示器"""
        self.status_toggle = not self.status_toggle
        if self.status_toggle:
            self.status_connected.set_status("connected")
            self.status_disconnected.set_status("disconnected")
        else:
            self.status_connected.set_status("disconnected")
            self.status_disconnected.set_status("connected")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("现代化UI演示")
    app.setApplicationVersion("1.0")
    
    # 设置字体
    font = QFont("Inter", 10)
    font.setStyleHint(QFont.SansSerif)
    app.setFont(font)
    
    # 创建演示窗口
    demo = ModernUIDemo()
    demo.show()
    
    print("🎨 现代化UI演示启动成功！")
    print("✨ 特色功能:")
    print("   • 科技感浅色主题")
    print("   • Neumorphism新拟态设计")
    print("   • 现代化组件库")
    print("   • 流畅动画效果")
    print("   • 智能状态指示")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
