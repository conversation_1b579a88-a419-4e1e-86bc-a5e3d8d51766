# -*- coding: utf-8 -*-
"""
现代化UI组件库
包含各种现代化的UI组件，如卡片、按钮、输入框等
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, 
    QFrame, QGraphicsDropShadowEffect, QScrollArea, QGroupBox,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox,
    QSlider, QProgressBar, QTextEdit, QFormLayout
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty, QRect
from PyQt5.QtGui import QPainter, QColor, QLinearGradient, QBrush, QPen, QFont
from modern_ui_styles import ModernUIStyles

class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()
        self.setup_shadow()
    
    def setup_ui(self):
        """设置UI"""
        self.setStyleSheet(ModernUIStyles.get_modern_card_style())
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(15)
        self.layout.setContentsMargins(20, 20, 20, 20)
        
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14pt;
                    font-weight: 700;
                    color: {ModernUIStyles.COLORS['primary']};
                    padding: 0 0 10px 0;
                    border-bottom: 2px solid {ModernUIStyles.COLORS['border']};
                    margin-bottom: 15px;
                }}
            """)
            self.layout.addWidget(title_label)
    
    def setup_shadow(self):
        """设置阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(shadow)
    
    def add_widget(self, widget):
        """添加组件"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局"""
        self.layout.addLayout(layout)

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        self.setup_animations()
    
    def setup_style(self):
        """设置样式"""
        if self.button_type == "success":
            self.setStyleSheet(ModernUIStyles.get_success_button_style())
        else:
            self.setStyleSheet(ModernUIStyles.get_modern_button_style())
    
    def setup_animations(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

class ModernInput(QLineEdit):
    """现代化输入框组件"""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class ModernSpinBox(QSpinBox):
    """现代化数字输入框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class ModernDoubleSpinBox(QDoubleSpinBox):
    """现代化浮点数输入框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(ModernUIStyles.get_modern_input_style())

class ModernComboBox(QComboBox):
    """现代化下拉框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(ModernUIStyles.get_modern_combobox_style())

class ModernCheckBox(QCheckBox):
    """现代化复选框组件"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet(ModernUIStyles.get_modern_checkbox_style())

class ModernSlider(QSlider):
    """现代化滑块组件"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setStyleSheet(ModernUIStyles.get_modern_slider_style())

class StatusIndicator(QLabel):
    """状态指示器组件"""
    
    def __init__(self, status="disconnected", parent=None):
        super().__init__(parent)
        self.status = status
        self.update_status(status)
    
    def update_status(self, status):
        """更新状态"""
        self.status = status
        if status == "connected":
            self.setText("✅ 已连接")
            self.setStyleSheet(ModernUIStyles.get_status_label_style("connected"))
        else:
            self.setText("❌ 未连接")
            self.setStyleSheet(ModernUIStyles.get_status_label_style("disconnected"))

class ModernGroupBox(QGroupBox):
    """现代化分组框"""
    
    def __init__(self, title="", parent=None):
        super().__init__(title, parent)
        self.setStyleSheet(ModernUIStyles.get_modern_card_style())
        self.setup_shadow()
    
    def setup_shadow(self):
        """设置阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 20))
        self.setGraphicsEffect(shadow)

class ModernFormLayout(QFormLayout):
    """现代化表单布局"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSpacing(15)
        self.setContentsMargins(0, 0, 0, 0)
        self.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
    
    def addRow(self, label_text, widget):
        """添加行"""
        if isinstance(label_text, str):
            label = QLabel(label_text)
            label.setStyleSheet(f"""
                QLabel {{
                    font-weight: 600;
                    color: {ModernUIStyles.COLORS['text_primary']};
                    font-size: 11pt;
                    padding-right: 10px;
                }}
            """)
            super().addRow(label, widget)
        else:
            super().addRow(label_text, widget)

class GlassMorphismWidget(QWidget):
    """玻璃拟态组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            GlassMorphismWidget {{
                background: {ModernUIStyles.COLORS['glass']};
                border: 1px solid rgba(255, 255, 255, 0.18);
                border-radius: 16px;
            }}
        """)
        self.setAttribute(Qt.WA_TranslucentBackground)

class AnimatedProgressBar(QProgressBar):
    """动画进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
        self.setup_animation()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernUIStyles.COLORS['border']};
                border-radius: 8px;
                text-align: center;
                background: {ModernUIStyles.COLORS['surface']};
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
            }}
            
            QProgressBar::chunk {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border-radius: 6px;
                margin: 2px;
            }}
        """)
    
    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def animate_to_value(self, value):
        """动画到指定值"""
        self.animation.setStartValue(self.value())
        self.animation.setEndValue(value)
        self.animation.start()

class ModernScrollArea(QScrollArea):
    """现代化滚动区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background: transparent;
            }}
            
            QScrollBar:vertical {{
                background: {ModernUIStyles.COLORS['surface']};
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }}
            
            QScrollBar::handle:vertical {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: transparent;
            }}
        """)

class FeatureCard(ModernCard):
    """功能卡片组件"""
    
    def __init__(self, title, description="", parent=None):
        super().__init__(title, parent)
        self.description = description
        self.enabled = False
        self.setup_content()
    
    def setup_content(self):
        """设置内容"""
        if self.description:
            desc_label = QLabel(self.description)
            desc_label.setStyleSheet(f"""
                QLabel {{
                    color: {ModernUIStyles.COLORS['text_secondary']};
                    font-size: 10pt;
                    padding: 5px 0;
                    line-height: 1.4;
                }}
            """)
            desc_label.setWordWrap(True)
            self.add_widget(desc_label)
        
        # 添加启用开关
        self.enable_checkbox = ModernCheckBox("启用此功能")
        self.enable_checkbox.stateChanged.connect(self.on_enable_changed)
        self.add_widget(self.enable_checkbox)
        
        # 添加配置区域
        self.config_widget = QWidget()
        self.config_layout = ModernFormLayout(self.config_widget)
        self.add_widget(self.config_widget)
        
        # 初始禁用配置区域
        self.config_widget.setEnabled(False)
    
    def on_enable_changed(self, state):
        """启用状态改变"""
        self.enabled = state == Qt.Checked
        self.config_widget.setEnabled(self.enabled)
    
    def add_config_widget(self, label, widget):
        """添加配置组件"""
        self.config_layout.addRow(label, widget)
