@echo off
chcp 65001 >nul
echo 🚀 启动现代化OBS去重软件...
echo.

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 🎨 启动独立现代化界面...
python standalone_modern_ui.py
if errorlevel 1 (
    echo.
    echo ❌ 独立版本启动失败，尝试测试版本...
    echo.
    python test_modern_ui.py
    if errorlevel 1 (
        echo.
        echo ❌ 测试版本也失败，尝试完整版本...
        echo.
        python modern_main.py
    )
)

echo.
echo 🎉 程序运行完成
pause
