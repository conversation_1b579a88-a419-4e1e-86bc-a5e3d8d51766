# -*- coding: utf-8 -*-
"""
现代化主窗口
基于原有功能，采用现代化UI设计
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QScrollArea, QFrame, QSplitter, QApplication, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QIcon
from modern_ui_styles import ModernUIStyles
from modern_ui_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernCheckBox, ModernSlider, StatusIndicator, GlassMorphismWidget,
    ModernSpinBox, ModernDoubleSpinBox, FeatureCard, ModernScrollArea,
    ModernFormLayout
)

class ModernMainWindow(QWidget):
    """现代化主窗口类"""
    
    def __init__(self, original_window, parent=None):
        super().__init__(parent)
        self.original_window = original_window  # 保持对原窗口的引用
        self.setup_window()
        self.create_ui()
        self.setup_animations()
        self.connect_signals()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle('OBS 去重软件 v2.0 - 现代化版本')
        self.setGeometry(100, 50, 1200, 900)
        self.setMinimumSize(1200, 900)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('obs2.ico'))
        except:
            pass
        
        # 应用现代化样式
        self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_ui(self):
        """创建用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建头部区域
        self.create_header(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
    
    def create_header(self, parent_layout):
        """创建头部区域"""
        header_card = ModernCard()
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)
        
        # 应用标题和副标题
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)
        
        app_title = QLabel("🎬 OBS 去重软件")
        app_title.setStyleSheet(f"""
            QLabel {{
                font-size: 24pt;
                font-weight: 700;
                color: {ModernUIStyles.COLORS['primary']};
                margin: 0;
                padding: 0;
            }}
        """)
        
        app_subtitle = QLabel("现代化科技感界面 • 智能去重解决方案")
        app_subtitle.setStyleSheet(f"""
            QLabel {{
                font-size: 12pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                margin: 0;
                padding: 0;
            }}
        """)
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)
        header_layout.addLayout(title_layout)
        
        # 连接状态和控制按钮
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        
        # 状态指示器
        self.status_indicator = StatusIndicator("disconnected")
        controls_layout.addWidget(self.status_indicator)
        
        # 连接按钮
        self.connect_btn = ModernButton("🔗 连接到 OBS", "primary")
        self.connect_btn.clicked.connect(self.on_connect_clicked)
        controls_layout.addWidget(self.connect_btn)
        
        # 一键启动按钮
        self.quick_start_btn = ModernButton("🚀 一键启动", "success")
        self.quick_start_btn.clicked.connect(self.on_quick_start_clicked)
        self.quick_start_btn.setEnabled(False)
        controls_layout.addWidget(self.quick_start_btn)
        
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        header_card.add_layout(header_layout)
        parent_layout.addWidget(header_card)
    
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background: {ModernUIStyles.COLORS['border']};
                width: 2px;
            }}
            QSplitter::handle:hover {{
                background: {ModernUIStyles.COLORS['primary']};
            }}
        """)
        
        # 左侧：功能配置区域
        self.create_config_panel(splitter)
        
        # 右侧：主要功能区域
        self.create_function_tabs(splitter)
        
        # 设置分割比例
        splitter.setSizes([350, 850])
        parent_layout.addWidget(splitter)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_scroll = ModernScrollArea()
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(15)
        config_layout.setContentsMargins(10, 10, 10, 10)
        
        # 媒体源选择卡片
        media_card = ModernCard("📺 媒体源选择")
        
        # 视频媒体源
        video_layout = QHBoxLayout()
        video_label = QLabel("视频源:")
        video_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.video_source_combo = ModernComboBox()
        refresh_video_btn = ModernButton("🔄", "primary")
        refresh_video_btn.setMaximumWidth(40)
        refresh_video_btn.clicked.connect(self.refresh_media_sources)
        
        video_layout.addWidget(video_label)
        video_layout.addWidget(self.video_source_combo, 1)
        video_layout.addWidget(refresh_video_btn)
        media_card.add_layout(video_layout)
        
        # 音频媒体源
        audio_layout = QHBoxLayout()
        audio_label = QLabel("音频源:")
        audio_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.audio_source_combo = ModernComboBox()
        refresh_audio_btn = ModernButton("🔄", "primary")
        refresh_audio_btn.setMaximumWidth(40)
        refresh_audio_btn.clicked.connect(self.refresh_media_sources)
        
        audio_layout.addWidget(audio_label)
        audio_layout.addWidget(self.audio_source_combo, 1)
        audio_layout.addWidget(refresh_audio_btn)
        media_card.add_layout(audio_layout)
        
        config_layout.addWidget(media_card)
        
        # 快速设置卡片
        quick_settings_card = ModernCard("⚡ 快速设置")
        
        # 预设配置
        preset_layout = QHBoxLayout()
        preset_label = QLabel("预设:")
        preset_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.preset_combo = ModernComboBox()
        self.preset_combo.addItems(["轻度去重", "中度去重", "重度去重", "自定义"])
        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.preset_combo, 1)
        quick_settings_card.add_layout(preset_layout)
        
        # 全局开关
        self.global_enable_checkbox = ModernCheckBox("启用全局去重")
        quick_settings_card.add_widget(self.global_enable_checkbox)
        
        config_layout.addWidget(quick_settings_card)
        
        # 系统状态卡片
        status_card = ModernCard("📊 系统状态")
        
        self.cpu_label = QLabel("CPU: 0%")
        self.memory_label = QLabel("内存: 0%")
        self.connection_label = QLabel("连接: 未连接")
        
        for label in [self.cpu_label, self.memory_label, self.connection_label]:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {ModernUIStyles.COLORS['text_secondary']};
                    font-size: 10pt;
                    padding: 5px;
                    background: {ModernUIStyles.COLORS['background']};
                    border-radius: 6px;
                }}
            """)
            status_card.add_widget(label)
        
        config_layout.addWidget(status_card)
        config_layout.addStretch()
        
        config_scroll.setWidget(config_widget)
        config_scroll.setWidgetResizable(True)
        parent.addWidget(config_scroll)
    
    def create_function_tabs(self, parent):
        """创建功能标签页"""
        # 创建现代化标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(ModernUIStyles.get_modern_tab_style())
        
        # 视频去重标签页
        self.create_video_tab()
        
        # 音频去重标签页
        self.create_audio_tab()
        
        # 高级功能标签页
        self.create_advanced_tab()
        
        # 说明文档标签页
        self.create_help_tab()
        
        parent.addWidget(self.tab_widget)
    
    def create_video_tab(self):
        """创建视频去重标签页"""
        video_widget = QWidget()
        video_scroll = ModernScrollArea()
        video_content = QWidget()
        video_layout = QVBoxLayout(video_content)
        video_layout.setSpacing(20)
        video_layout.setContentsMargins(20, 20, 20, 20)
        
        # 智能加减速卡片
        speed_card = FeatureCard(
            "🚀 智能加减速",
            "根据视频播放状态自动调整播放速度，有效避免重复检测"
        )
        
        # 速度范围设置
        speed_range_layout = QHBoxLayout()
        
        min_speed_layout = QVBoxLayout()
        min_speed_label = QLabel("最低速度")
        min_speed_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.speed_min_slider = ModernSlider()
        self.speed_min_slider.setRange(1, 200)
        self.speed_min_slider.setValue(90)
        self.speed_min_spinbox = ModernSpinBox()
        self.speed_min_spinbox.setRange(1, 200)
        self.speed_min_spinbox.setValue(90)
        self.speed_min_spinbox.setSuffix(" %")
        
        min_speed_layout.addWidget(min_speed_label)
        min_speed_layout.addWidget(self.speed_min_slider)
        min_speed_layout.addWidget(self.speed_min_spinbox)
        
        max_speed_layout = QVBoxLayout()
        max_speed_label = QLabel("最高速度")
        max_speed_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.speed_max_slider = ModernSlider()
        self.speed_max_slider.setRange(1, 200)
        self.speed_max_slider.setValue(120)
        self.speed_max_spinbox = ModernSpinBox()
        self.speed_max_spinbox.setRange(1, 200)
        self.speed_max_spinbox.setValue(120)
        self.speed_max_spinbox.setSuffix(" %")
        
        max_speed_layout.addWidget(max_speed_label)
        max_speed_layout.addWidget(self.speed_max_slider)
        max_speed_layout.addWidget(self.speed_max_spinbox)
        
        speed_range_layout.addLayout(min_speed_layout)
        speed_range_layout.addLayout(max_speed_layout)
        
        speed_range_widget = QWidget()
        speed_range_widget.setLayout(speed_range_layout)
        speed_card.add_config_widget("速度范围:", speed_range_widget)
        
        # 连接信号
        self.speed_min_slider.valueChanged.connect(self.speed_min_spinbox.setValue)
        self.speed_min_spinbox.valueChanged.connect(self.speed_min_slider.setValue)
        self.speed_max_slider.valueChanged.connect(self.speed_max_spinbox.setValue)
        self.speed_max_spinbox.valueChanged.connect(self.speed_max_slider.setValue)
        
        video_layout.addWidget(speed_card)
        
        # 模糊去重卡片
        blur_card = FeatureCard(
            "🌫️ 模糊去重",
            "通过动态调整模糊效果来改变视频特征，需要安装对应插件"
        )
        
        # 模糊设置
        self.blur_filter_name_edit = ModernInput("Composite Blur")
        blur_card.add_config_widget("滤镜名称:", self.blur_filter_name_edit)
        
        self.blur_min_radius_spin = ModernDoubleSpinBox()
        self.blur_min_radius_spin.setRange(0.0, 50.0)
        self.blur_min_radius_spin.setValue(0.0)
        blur_card.add_config_widget("最小半径:", self.blur_min_radius_spin)
        
        self.blur_max_radius_spin = ModernDoubleSpinBox()
        self.blur_max_radius_spin.setRange(0.0, 50.0)
        self.blur_max_radius_spin.setValue(2.0)
        blur_card.add_config_widget("最大半径:", self.blur_max_radius_spin)
        
        video_layout.addWidget(blur_card)
        video_layout.addStretch()
        
        video_scroll.setWidget(video_content)
        video_scroll.setWidgetResizable(True)
        self.tab_widget.addTab(video_scroll, "🎬 视频去重")
    
    def create_audio_tab(self):
        """创建音频去重标签页"""
        audio_widget = QWidget()
        audio_scroll = ModernScrollArea()
        audio_content = QWidget()
        audio_layout = QVBoxLayout(audio_content)
        audio_layout.setSpacing(20)
        audio_layout.setContentsMargins(20, 20, 20, 20)

        # 音频EQ去重卡片
        eq_card = FeatureCard(
            "🔊 音频EQ去重",
            "通过动态调整音频均衡器参数来改变音频特征"
        )

        # EQ设置
        self.audio_filter_name_edit = ModernInput("3段式均衡器")
        eq_card.add_config_widget("滤镜名称:", self.audio_filter_name_edit)

        # 低频设置
        low_freq_layout = QHBoxLayout()
        self.audio_low_min_spin = ModernDoubleSpinBox()
        self.audio_low_min_spin.setRange(-2.0, 2.0)
        self.audio_low_min_spin.setValue(-2.0)
        self.audio_low_min_spin.setSuffix(" dB")
        self.audio_low_max_spin = ModernDoubleSpinBox()
        self.audio_low_max_spin.setRange(-2.0, 2.0)
        self.audio_low_max_spin.setValue(2.0)
        self.audio_low_max_spin.setSuffix(" dB")
        low_freq_layout.addWidget(QLabel("最小:"))
        low_freq_layout.addWidget(self.audio_low_min_spin)
        low_freq_layout.addWidget(QLabel("最大:"))
        low_freq_layout.addWidget(self.audio_low_max_spin)
        low_freq_widget = QWidget()
        low_freq_widget.setLayout(low_freq_layout)
        eq_card.add_config_widget("低频范围:", low_freq_widget)

        # 断音控制卡片
        mute_card = FeatureCard(
            "🔇 断音控制",
            "随机在音频中插入短暂的静音，模拟自然的音频变化"
        )

        # 断音间隔设置
        interval_layout = QHBoxLayout()
        self.mute_interval_min_spin = ModernDoubleSpinBox()
        self.mute_interval_min_spin.setRange(10.0, 120.0)
        self.mute_interval_min_spin.setValue(30.0)
        self.mute_interval_min_spin.setSuffix(" s")
        self.mute_interval_max_spin = ModernDoubleSpinBox()
        self.mute_interval_max_spin.setRange(10.0, 120.0)
        self.mute_interval_max_spin.setValue(40.0)
        self.mute_interval_max_spin.setSuffix(" s")
        interval_layout.addWidget(QLabel("最小:"))
        interval_layout.addWidget(self.mute_interval_min_spin)
        interval_layout.addWidget(QLabel("最大:"))
        interval_layout.addWidget(self.mute_interval_max_spin)
        interval_widget = QWidget()
        interval_widget.setLayout(interval_layout)
        mute_card.add_config_widget("断音间隔:", interval_widget)

        audio_layout.addWidget(eq_card)
        audio_layout.addWidget(mute_card)
        audio_layout.addStretch()

        audio_scroll.setWidget(audio_content)
        audio_scroll.setWidgetResizable(True)
        self.tab_widget.addTab(audio_scroll, "🎵 音频去重")
    
    def create_advanced_tab(self):
        """创建高级功能标签页"""
        advanced_widget = QWidget()
        advanced_layout = QVBoxLayout(advanced_widget)
        advanced_layout.setContentsMargins(20, 20, 20, 20)
        
        placeholder_label = QLabel("⚙️ 高级功能正在开发中...")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                padding: 50px;
            }}
        """)
        advanced_layout.addWidget(placeholder_label)
        
        self.tab_widget.addTab(advanced_widget, "⚙️ 高级功能")
    
    def create_help_tab(self):
        """创建帮助标签页"""
        help_widget = QWidget()
        help_layout = QVBoxLayout(help_widget)
        help_layout.setContentsMargins(20, 20, 20, 20)
        
        placeholder_label = QLabel("📖 使用说明正在编写中...")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                padding: 50px;
            }}
        """)
        help_layout.addWidget(placeholder_label)
        
        self.tab_widget.addTab(help_widget, "📖 使用说明")
    
    def setup_animations(self):
        """设置动画效果"""
        # 窗口淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 启动淡入动画
        QTimer.singleShot(100, self.fade_animation.start)
    
    def connect_signals(self):
        """连接信号"""
        # 连接原窗口的信号到新窗口
        if hasattr(self.original_window, 'video_source_combo'):
            self.video_source_combo.currentTextChanged.connect(
                self.original_window.video_source_combo.setCurrentText
            )
        
        if hasattr(self.original_window, 'audio_source_combo'):
            self.audio_source_combo.currentTextChanged.connect(
                self.original_window.audio_source_combo.setCurrentText
            )
    
    def on_connect_clicked(self):
        """连接按钮点击事件"""
        if hasattr(self.original_window, 'connect_to_obs'):
            self.original_window.connect_to_obs()
            # 更新状态
            if self.original_window.is_connected:
                self.status_indicator.update_status("connected")
                self.connect_btn.setText("🔗 已连接")
                self.quick_start_btn.setEnabled(True)
    
    def on_quick_start_clicked(self):
        """一键启动按钮点击事件"""
        if hasattr(self.original_window, 'show_quick_start_dialog'):
            self.original_window.show_quick_start_dialog()
    
    def refresh_media_sources(self):
        """刷新媒体源"""
        if hasattr(self.original_window, 'refresh_media_sources'):
            self.original_window.refresh_media_sources()
            # 更新下拉框
            if hasattr(self.original_window, 'video_source_combo'):
                self.video_source_combo.clear()
                for i in range(self.original_window.video_source_combo.count()):
                    self.video_source_combo.addItem(
                        self.original_window.video_source_combo.itemText(i)
                    )
            
            if hasattr(self.original_window, 'audio_source_combo'):
                self.audio_source_combo.clear()
                for i in range(self.original_window.audio_source_combo.count()):
                    self.audio_source_combo.addItem(
                        self.original_window.audio_source_combo.itemText(i)
                    )
