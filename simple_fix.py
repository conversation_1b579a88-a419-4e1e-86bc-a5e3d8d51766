#!/usr/bin/env python
# -*- coding: utf-8 -*-

def simple_fix():
    """简单修复qlineargradient问题"""
    
    # 读取文件
    with open('main_module.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 简单替换所有qlineargradient为background-color
    import re
    
    # 替换所有qlineargradient为简单的背景色
    content = re.sub(r'background:\s*qlineargradient\([^)]+\)', 'background-color: white', content, flags=re.MULTILINE | re.DOTALL)
    content = re.sub(r'selection-background-color:\s*qlineargradient\([^)]+\)', 'selection-background-color: #667eea', content, flags=re.MULTILINE | re.DOTALL)
    
    # 移除不支持的CSS属性
    content = re.sub(r'backdrop-filter:[^;]+;', '', content)
    content = re.sub(r'box-shadow:[^;]+;', '', content)
    content = re.sub(r'transform:[^;]+;', '', content)
    
    # 写回文件
    with open('main_module.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 简单修复完成")

if __name__ == '__main__':
    simple_fix()
