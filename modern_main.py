# -*- coding: utf-8 -*-
"""
现代化OBS去重软件启动文件
采用科技感浅色主题设计
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont, QFontDatabase
from modern_ui_styles import ModernUIStyles
from modern_main_window import ModernMainWindow

# 导入原始主模块
try:
    from main_module import MainWindow
except ImportError as e:
    print(f"无法导入原始模块: {e}")
    MainWindow = None

def setup_fonts():
    """设置字体"""
    try:
        # 尝试加载Inter字体（如果有的话）
        font_db = QFontDatabase()
        
        # 设置默认字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(11)
        QApplication.setFont(font)
        
        print("✅ 字体设置完成")
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")

def setup_application_style(app):
    """设置应用程序样式"""
    try:
        # 应用全局样式
        app.setStyleSheet(f"""
            * {{
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            }}
            
            QApplication {{
                background: {ModernUIStyles.COLORS['background']};
            }}
            
            QToolTip {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
                border: 1px solid {ModernUIStyles.COLORS['border']};
                border-radius: 6px;
                padding: 8px;
                font-size: 10pt;
            }}
            
            QMessageBox {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
            }}
            
            QMessageBox QPushButton {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 600;
                min-width: 80px;
            }}
            
            QMessageBox QPushButton:hover {{
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }}
        """)
        
        print("✅ 应用样式设置完成")
    except Exception as e:
        print(f"⚠️ 样式设置失败: {e}")

def create_splash_screen():
    """创建启动画面"""
    try:
        # 创建启动画面
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.white)
        
        splash = QSplashScreen(splash_pixmap)
        splash.setStyleSheet(f"""
            QSplashScreen {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                color: white;
                font-size: 14pt;
                font-weight: 600;
                border-radius: 12px;
            }}
        """)
        
        splash.show()
        splash.showMessage("🚀 正在启动现代化OBS去重软件...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        
        return splash
    except Exception as e:
        print(f"⚠️ 启动画面创建失败: {e}")
        return None

def show_welcome_message():
    """显示欢迎消息"""
    try:
        msg = QMessageBox()
        msg.setWindowTitle("欢迎使用")
        msg.setIcon(QMessageBox.Information)
        msg.setText("🎉 欢迎使用现代化OBS去重软件!")
        msg.setInformativeText("""
✨ 全新的科技感界面设计
🎬 智能视频去重功能
🎵 高级音频处理
⚡ 一键快速启动
        """)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setStyleSheet(f"""
            QMessageBox {{
                background: {ModernUIStyles.COLORS['surface']};
                color: {ModernUIStyles.COLORS['text_primary']};
                font-size: 11pt;
            }}
            QMessageBox QLabel {{
                color: {ModernUIStyles.COLORS['text_primary']};
                font-size: 11pt;
            }}
        """)
        
        # 不阻塞显示
        msg.show()
        return msg
    except Exception as e:
        print(f"⚠️ 欢迎消息显示失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 启动现代化OBS去重软件...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("OBS去重软件")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("现代化软件工作室")
    
    # 设置应用程序属性
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    try:
        # 设置字体
        setup_fonts()
        
        # 设置应用样式
        setup_application_style(app)
        
        # 创建启动画面
        splash = create_splash_screen()
        if splash:
            app.processEvents()
        
        # 创建原始窗口实例（用于功能支持）
        print("📦 正在初始化核心功能...")
        if splash:
            splash.showMessage("📦 正在初始化核心功能...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
        
        original_window = None
        if MainWindow:
            try:
                original_window = MainWindow()
                print("✅ 核心功能初始化完成")
            except Exception as e:
                print(f"⚠️ 核心功能初始化失败: {e}")
                if splash:
                    splash.showMessage(f"⚠️ 核心功能初始化失败: {e}", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
                    app.processEvents()
        
        # 创建现代化主窗口
        print("🎨 正在创建现代化界面...")
        if splash:
            splash.showMessage("🎨 正在创建现代化界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
            app.processEvents()
        
        main_window = ModernMainWindow(original_window)
        
        # 关闭启动画面
        if splash:
            QTimer.singleShot(2000, splash.close)
        
        # 显示主窗口
        main_window.show()
        
        # 显示欢迎消息
        QTimer.singleShot(2500, show_welcome_message)
        
        print("✅ 现代化OBS去重软件启动完成!")
        print("🌟 享受全新的用户体验!")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        
        # 显示错误消息
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Critical)
        error_msg.setWindowTitle("启动错误")
        error_msg.setText("应用程序启动失败")
        error_msg.setInformativeText(f"错误详情: {str(e)}")
        error_msg.setStandardButtons(QMessageBox.Ok)
        error_msg.exec_()
        
        return 1

if __name__ == "__main__":
    # 设置环境变量
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
    
    # 运行应用程序
    sys.exit(main())
