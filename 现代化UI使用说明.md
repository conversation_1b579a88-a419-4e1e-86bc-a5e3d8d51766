# 🎨 现代化OBS去重软件 - 使用说明

## 🚀 快速启动

### 方法一：双击批处理文件
```
双击 run_modern_ui.bat 文件
```

### 方法二：命令行启动
```bash
# 独立现代化版本（推荐）
python standalone_modern_ui.py

# 完整功能版本
python modern_main.py

# 测试版本
python test_modern_ui.py
```

## 📁 文件说明

### 核心文件
- `standalone_modern_ui.py` - 独立现代化界面（推荐使用）
- `modern_main.py` - 完整现代化界面（需要原始功能支持）
- `test_modern_ui.py` - 测试版本
- `run_modern_ui.bat` - 一键启动批处理文件

### 样式和组件
- `modern_ui_styles.py` - 现代化样式定义
- `modern_ui_components.py` - 现代化UI组件库
- `modern_ui_config.py` - 配置管理

### 文档
- `MODERN_UI_README.md` - 详细技术文档
- `现代化UI使用说明.md` - 本文件

## 🎯 界面特色

### ✨ 视觉设计
- **科技感浅色主题**: 清新的蓝白配色
- **圆角设计**: 现代化的圆角元素
- **渐变效果**: 精美的渐变背景
- **阴影效果**: 立体感的卡片阴影
- **现代化字体**: 清晰易读的字体

### 🎮 交互体验
- **悬停效果**: 鼠标悬停时的视觉反馈
- **点击动画**: 按钮点击时的动画效果
- **状态指示**: 清晰的连接状态显示
- **响应式布局**: 自适应窗口大小

## 🔧 功能说明

### 📺 媒体源选择
- 在左侧配置面板选择视频源和音频源
- 支持多种媒体源类型
- 实时刷新媒体源列表

### ⚡ 快速设置
- **预设配置**: 轻度/中度/重度去重预设
- **全局开关**: 一键启用/禁用所有功能
- **一键启动**: 快速启动所有去重功能

### 🎬 视频去重功能
- **智能加减速**: 动态调整播放速度
- **模糊去重**: 动态模糊效果
- **移动去重**: 视频位置变化
- **颜色去重**: 颜色参数调整

### 🎵 音频去重功能
- **音频EQ**: 均衡器参数调整
- **断音控制**: 随机静音效果
- **音量控制**: 动态音量变化

## 🎨 主题配置

### 默认主题色彩
- **主色调**: #667eea (科技蓝)
- **辅助色**: #764ba2 (深紫)
- **背景色**: #f8fafc (浅灰白)
- **成功色**: #10b981 (翠绿)
- **错误色**: #ef4444 (红色)

### 自定义主题
1. 打开 `modern_ui_config.py`
2. 修改 `COLORS` 字典中的颜色值
3. 重启应用程序查看效果

## 🔧 故障排除

### 常见问题

#### 1. 程序无法启动
**解决方案**:
- 确保已安装Python 3.7+
- 确保已安装PyQt5: `pip install PyQt5`
- 检查文件路径是否正确

#### 2. 界面显示异常
**解决方案**:
- 更新显卡驱动
- 尝试运行 `test_modern_ui.py` 测试版本
- 检查系统DPI设置

#### 3. 功能无法使用
**解决方案**:
- 确保OBS Studio正在运行
- 检查WebSocket插件是否安装
- 验证连接设置是否正确

### 调试模式
```bash
# 启用详细输出
python standalone_modern_ui.py --verbose

# 查看错误信息
python -u standalone_modern_ui.py
```

## 📋 系统要求

### 最低要求
- Windows 10/11
- Python 3.7+
- PyQt5
- 2GB RAM
- 1GB 可用磁盘空间

### 推荐配置
- Windows 11
- Python 3.9+
- 4GB+ RAM
- 支持硬件加速的显卡
- 高分辨率显示器

## 🎉 使用技巧

### 1. 快速上手
1. 双击 `run_modern_ui.bat` 启动程序
2. 点击"连接到OBS"按钮
3. 选择合适的预设配置
4. 点击"一键启动"开始使用

### 2. 自定义配置
1. 在左侧面板选择媒体源
2. 切换到对应的功能标签页
3. 调整参数到合适的值
4. 启用相应的功能开关

### 3. 性能优化
- 关闭不需要的功能以节省资源
- 适当调整参数范围避免过度处理
- 定期重启程序清理内存

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看控制台输出**: 运行时的错误信息
2. **检查配置文件**: 确保配置正确
3. **尝试不同版本**: 测试版本或完整版本
4. **重置配置**: 删除配置文件重新开始

## 🔄 版本更新

### v2.0 现代化版本
- ✅ 全新的现代化界面设计
- ✅ 科技感浅色主题
- ✅ 响应式布局和动画效果
- ✅ 模块化组件架构
- ✅ 独立运行模式

### 未来计划
- 🔄 更多主题选择
- 🔄 高级动画效果
- 🔄 插件系统
- 🔄 云端配置同步

---

**享受现代化的OBS去重体验！** 🌟
