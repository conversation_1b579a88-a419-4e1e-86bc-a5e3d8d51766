# -*- coding: utf-8 -*-
"""
独立现代化OBS去重软件界面
不依赖原始main_module.py，可独立运行
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
    QLabel, QSplitter, QMessageBox, QCheckBox, QSpinBox, QDoubleSpinBox,
    QComboBox, QLineEdit, QPushButton, QSlider, QFormLayout, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QIcon

# 导入现代化UI组件
try:
    from modern_ui_styles import ModernUIStyles
    from modern_ui_components import (
        ModernCard, ModernButton, ModernInput, ModernComboBox,
        ModernCheckBox, ModernSlider, StatusIndicator, 
        ModernSpinBox, ModernDoubleSpinBox, FeatureCard, ModernScrollArea
    )
    MODERN_UI_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 现代化UI组件导入失败: {e}")
    MODERN_UI_AVAILABLE = False

class StandaloneModernWindow(QWidget):
    """独立现代化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_ui()
        self.setup_demo_data()
        
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle('OBS 去重软件 v2.0 - 现代化独立版')
        self.setGeometry(100, 50, 1200, 900)
        self.setMinimumSize(1000, 700)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('obs2.ico'))
        except:
            pass
        
        # 应用现代化样式
        if MODERN_UI_AVAILABLE:
            self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_ui(self):
        """创建用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建头部区域
        self.create_header(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
    
    def create_header(self, parent_layout):
        """创建头部区域"""
        if MODERN_UI_AVAILABLE:
            header_card = ModernCard()
        else:
            header_card = QGroupBox()
            header_card.setStyleSheet("QGroupBox { padding: 15px; margin: 5px; }")
        
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)
        
        # 应用标题
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)
        
        app_title = QLabel("🎬 OBS 去重软件")
        if MODERN_UI_AVAILABLE:
            app_title.setStyleSheet(f"""
                QLabel {{
                    font-size: 24pt;
                    font-weight: 700;
                    color: {ModernUIStyles.COLORS['primary']};
                    margin: 0;
                    padding: 0;
                }}
            """)
        else:
            app_title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #667eea;")
        
        app_subtitle = QLabel("现代化科技感界面 • 智能去重解决方案")
        if MODERN_UI_AVAILABLE:
            app_subtitle.setStyleSheet(f"""
                QLabel {{
                    font-size: 12pt;
                    color: {ModernUIStyles.COLORS['text_secondary']};
                    margin: 0;
                    padding: 0;
                }}
            """)
        else:
            app_subtitle.setStyleSheet("font-size: 10pt; color: #64748b;")
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)
        header_layout.addLayout(title_layout)
        
        # 控制按钮
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        
        # 状态指示器
        if MODERN_UI_AVAILABLE:
            self.status_indicator = StatusIndicator("disconnected")
        else:
            self.status_indicator = QLabel("❌ 未连接")
            self.status_indicator.setStyleSheet("background: #ef4444; color: white; padding: 8px; border-radius: 4px;")
        controls_layout.addWidget(self.status_indicator)
        
        # 连接按钮
        if MODERN_UI_AVAILABLE:
            self.connect_btn = ModernButton("🔗 连接到 OBS", "primary")
        else:
            self.connect_btn = QPushButton("🔗 连接到 OBS")
            self.connect_btn.setStyleSheet("background: #667eea; color: white; padding: 10px; border-radius: 6px; font-weight: bold;")
        self.connect_btn.clicked.connect(self.on_connect_clicked)
        controls_layout.addWidget(self.connect_btn)
        
        # 一键启动按钮
        if MODERN_UI_AVAILABLE:
            self.quick_start_btn = ModernButton("🚀 一键启动", "success")
        else:
            self.quick_start_btn = QPushButton("🚀 一键启动")
            self.quick_start_btn.setStyleSheet("background: #10b981; color: white; padding: 10px; border-radius: 6px; font-weight: bold;")
        self.quick_start_btn.clicked.connect(self.on_quick_start_clicked)
        self.quick_start_btn.setEnabled(False)
        controls_layout.addWidget(self.quick_start_btn)
        
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        if MODERN_UI_AVAILABLE:
            header_card.add_layout(header_layout)
        else:
            header_card.setLayout(header_layout)
        parent_layout.addWidget(header_card)
    
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        if MODERN_UI_AVAILABLE:
            splitter.setStyleSheet(f"""
                QSplitter::handle {{
                    background: {ModernUIStyles.COLORS['border']};
                    width: 2px;
                }}
                QSplitter::handle:hover {{
                    background: {ModernUIStyles.COLORS['primary']};
                }}
            """)
        
        # 左侧：配置面板
        self.create_config_panel(splitter)
        
        # 右侧：功能标签页
        self.create_function_tabs(splitter)
        
        # 设置分割比例
        splitter.setSizes([300, 900])
        parent_layout.addWidget(splitter)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(15)
        config_layout.setContentsMargins(10, 10, 10, 10)
        
        # 媒体源选择
        if MODERN_UI_AVAILABLE:
            media_card = ModernCard("📺 媒体源选择")
        else:
            media_card = QGroupBox("📺 媒体源选择")
            media_card.setStyleSheet("QGroupBox { font-weight: bold; padding: 10px; }")
        
        media_layout = QVBoxLayout()
        
        # 视频源
        video_layout = QHBoxLayout()
        video_label = QLabel("视频源:")
        if MODERN_UI_AVAILABLE:
            self.video_source_combo = ModernComboBox()
        else:
            self.video_source_combo = QComboBox()
        video_layout.addWidget(video_label)
        video_layout.addWidget(self.video_source_combo, 1)
        media_layout.addLayout(video_layout)
        
        # 音频源
        audio_layout = QHBoxLayout()
        audio_label = QLabel("音频源:")
        if MODERN_UI_AVAILABLE:
            self.audio_source_combo = ModernComboBox()
        else:
            self.audio_source_combo = QComboBox()
        audio_layout.addWidget(audio_label)
        audio_layout.addWidget(self.audio_source_combo, 1)
        media_layout.addLayout(audio_layout)
        
        if MODERN_UI_AVAILABLE:
            media_card.add_layout(media_layout)
        else:
            media_card.setLayout(media_layout)
        config_layout.addWidget(media_card)
        
        # 快速设置
        if MODERN_UI_AVAILABLE:
            quick_card = ModernCard("⚡ 快速设置")
        else:
            quick_card = QGroupBox("⚡ 快速设置")
            quick_card.setStyleSheet("QGroupBox { font-weight: bold; padding: 10px; }")
        
        quick_layout = QVBoxLayout()
        
        # 预设选择
        preset_layout = QHBoxLayout()
        preset_label = QLabel("预设:")
        if MODERN_UI_AVAILABLE:
            self.preset_combo = ModernComboBox()
        else:
            self.preset_combo = QComboBox()
        self.preset_combo.addItems(["轻度去重", "中度去重", "重度去重", "自定义"])
        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.preset_combo, 1)
        quick_layout.addLayout(preset_layout)
        
        # 全局开关
        if MODERN_UI_AVAILABLE:
            self.global_enable_checkbox = ModernCheckBox("启用全局去重")
        else:
            self.global_enable_checkbox = QCheckBox("启用全局去重")
        quick_layout.addWidget(self.global_enable_checkbox)
        
        if MODERN_UI_AVAILABLE:
            quick_card.add_layout(quick_layout)
        else:
            quick_card.setLayout(quick_layout)
        config_layout.addWidget(quick_card)
        
        config_layout.addStretch()
        parent.addWidget(config_widget)
    
    def create_function_tabs(self, parent):
        """创建功能标签页"""
        self.tab_widget = QTabWidget()
        if MODERN_UI_AVAILABLE:
            self.tab_widget.setStyleSheet(ModernUIStyles.get_modern_tab_style())
        
        # 视频去重标签页
        self.create_video_tab()
        
        # 音频去重标签页
        self.create_audio_tab()
        
        # 设置标签页
        self.create_settings_tab()
        
        parent.addWidget(self.tab_widget)
    
    def create_video_tab(self):
        """创建视频去重标签页"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)
        video_layout.setSpacing(20)
        video_layout.setContentsMargins(20, 20, 20, 20)
        
        # 智能加减速
        if MODERN_UI_AVAILABLE:
            speed_card = FeatureCard("🚀 智能加减速", "根据视频播放状态自动调整播放速度")
        else:
            speed_card = QGroupBox("🚀 智能加减速")
            speed_card.setCheckable(True)
            speed_card.setStyleSheet("QGroupBox { font-weight: bold; padding: 15px; }")
        
        speed_form = QFormLayout()
        
        # 最低速度
        if MODERN_UI_AVAILABLE:
            self.speed_min_spin = ModernSpinBox()
        else:
            self.speed_min_spin = QSpinBox()
        self.speed_min_spin.setRange(1, 200)
        self.speed_min_spin.setValue(90)
        self.speed_min_spin.setSuffix(" %")
        speed_form.addRow("最低速度:", self.speed_min_spin)
        
        # 最高速度
        if MODERN_UI_AVAILABLE:
            self.speed_max_spin = ModernSpinBox()
        else:
            self.speed_max_spin = QSpinBox()
        self.speed_max_spin.setRange(1, 200)
        self.speed_max_spin.setValue(120)
        self.speed_max_spin.setSuffix(" %")
        speed_form.addRow("最高速度:", self.speed_max_spin)
        
        if MODERN_UI_AVAILABLE:
            speed_form_widget = QWidget()
            speed_form_widget.setLayout(speed_form)
            speed_card.add_widget(speed_form_widget)
        else:
            speed_card.setLayout(speed_form)
        
        video_layout.addWidget(speed_card)
        video_layout.addStretch()
        
        self.tab_widget.addTab(video_widget, "🎬 视频去重")
    
    def create_audio_tab(self):
        """创建音频去重标签页"""
        audio_widget = QWidget()
        audio_layout = QVBoxLayout(audio_widget)
        audio_layout.setContentsMargins(20, 20, 20, 20)
        
        placeholder_label = QLabel("🎵 音频去重功能")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet("font-size: 16pt; color: #64748b; padding: 50px;")
        audio_layout.addWidget(placeholder_label)
        
        self.tab_widget.addTab(audio_widget, "🎵 音频去重")
    
    def create_settings_tab(self):
        """创建设置标签页"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(20, 20, 20, 20)
        
        # 主题设置
        theme_group = QGroupBox("🎨 主题设置")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["科技感浅色主题", "深色主题", "高对比度主题"])
        theme_layout.addRow("主题:", self.theme_combo)
        
        settings_layout.addWidget(theme_group)
        settings_layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "⚙️ 设置")
    
    def setup_demo_data(self):
        """设置演示数据"""
        # 添加演示媒体源
        demo_sources = ["摄像头", "屏幕捕获", "窗口捕获", "媒体源1", "媒体源2"]
        self.video_source_combo.addItems(demo_sources)
        self.audio_source_combo.addItems(["麦克风", "系统音频", "音频源1", "音频源2"])
    
    def on_connect_clicked(self):
        """连接按钮点击事件"""
        if MODERN_UI_AVAILABLE:
            self.status_indicator.update_status("connected")
        else:
            self.status_indicator.setText("✅ 已连接")
            self.status_indicator.setStyleSheet("background: #10b981; color: white; padding: 8px; border-radius: 4px;")
        
        self.connect_btn.setText("🔗 已连接")
        self.quick_start_btn.setEnabled(True)
        
        # 显示连接成功消息
        QMessageBox.information(self, "连接成功", "已成功连接到OBS！\n（这是演示模式）")
    
    def on_quick_start_clicked(self):
        """一键启动按钮点击事件"""
        QMessageBox.information(self, "一键启动", "正在启动所有去重功能...\n（这是演示模式）")

def main():
    """主函数"""
    print("🚀 启动独立现代化OBS去重软件...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("OBS去重软件")
    app.setApplicationVersion("2.0")
    
    # 设置应用程序属性
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    try:
        # 创建主窗口
        window = StandaloneModernWindow()
        window.show()
        
        print("✅ 独立现代化界面启动成功！")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误消息
        try:
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("启动错误")
            error_msg.setText("应用程序启动失败")
            error_msg.setInformativeText(f"错误详情: {str(e)}")
            error_msg.exec_()
        except:
            pass
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
