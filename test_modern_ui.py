# -*- coding: utf-8 -*-
"""
现代化UI测试文件
用于测试现代化界面是否能正常运行
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("🧪 开始测试导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ PyQt5 导入成功")
    except ImportError as e:
        print(f"❌ PyQt5 导入失败: {e}")
        return False
    
    try:
        from modern_ui_styles import ModernUIStyles
        print("✅ 现代化样式模块导入成功")
    except ImportError as e:
        print(f"❌ 现代化样式模块导入失败: {e}")
        return False
    
    try:
        from modern_ui_components import ModernCard, ModernButton
        print("✅ 现代化组件模块导入成功")
    except ImportError as e:
        print(f"❌ 现代化组件模块导入失败: {e}")
        return False
    
    return True

def create_test_window():
    """创建测试窗口"""
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
    from PyQt5.QtCore import Qt
    from modern_ui_styles import ModernUIStyles
    from modern_ui_components import ModernCard, ModernButton
    
    class TestWindow(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("现代化UI测试")
            self.setGeometry(300, 300, 600, 400)
            
            # 应用样式
            self.setStyleSheet(ModernUIStyles.get_main_window_style())
            
            # 创建布局
            layout = QVBoxLayout(self)
            layout.setSpacing(20)
            layout.setContentsMargins(20, 20, 20, 20)
            
            # 标题
            title = QLabel("🎨 现代化UI测试")
            title.setStyleSheet(f"""
                QLabel {{
                    font-size: 24pt;
                    font-weight: 700;
                    color: {ModernUIStyles.COLORS['primary']};
                    text-align: center;
                    padding: 20px;
                }}
            """)
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # 测试卡片
            card = ModernCard("测试卡片")
            
            # 测试按钮
            test_btn = ModernButton("测试按钮", "primary")
            test_btn.clicked.connect(self.on_test_clicked)
            card.add_widget(test_btn)
            
            success_btn = ModernButton("成功按钮", "success")
            success_btn.clicked.connect(self.on_success_clicked)
            card.add_widget(success_btn)
            
            layout.addWidget(card)
            
            # 状态标签
            self.status_label = QLabel("✅ 现代化UI测试成功！")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    background: {ModernUIStyles.COLORS['success']};
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 12pt;
                }}
            """)
            self.status_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(self.status_label)
        
        def on_test_clicked(self):
            self.status_label.setText("🔵 测试按钮被点击！")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    background: {ModernUIStyles.COLORS['info']};
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 12pt;
                }}
            """)
        
        def on_success_clicked(self):
            self.status_label.setText("✅ 成功按钮被点击！")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    background: {ModernUIStyles.COLORS['success']};
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    font-weight: 600;
                    font-size: 12pt;
                }}
            """)
    
    return TestWindow

def main():
    """主函数"""
    print("🚀 启动现代化UI测试...")
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，无法继续")
        return 1
    
    print("✅ 所有导入测试通过")
    
    # 创建应用程序
    try:
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("现代化UI测试")
        
        print("✅ QApplication 创建成功")
        
        # 创建测试窗口
        TestWindow = create_test_window()
        window = TestWindow()
        window.show()
        
        print("✅ 测试窗口创建成功")
        print("🎉 现代化UI测试启动完成！")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 应用程序创建失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
