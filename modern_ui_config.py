# -*- coding: utf-8 -*-
"""
现代化UI配置管理
管理主题、动画、布局等配置选项
"""

import json
import os
from typing import Dict, Any, Optional

class ModernUIConfig:
    """现代化UI配置管理类"""
    
    def __init__(self, config_file="modern_ui_config.json"):
        self.config_file = config_file
        self.default_config = self.get_default_config()
        self.current_config = self.load_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "theme": {
                "name": "科技感浅色主题",
                "primary_color": "#667eea",
                "secondary_color": "#764ba2",
                "background_color": "#f8fafc",
                "surface_color": "#ffffff",
                "text_primary": "#1e293b",
                "text_secondary": "#64748b",
                "success_color": "#10b981",
                "warning_color": "#f59e0b",
                "error_color": "#ef4444",
                "info_color": "#3b82f6",
                "border_color": "#e2e8f0"
            },
            "animations": {
                "enabled": True,
                "duration": 300,
                "easing": "OutCubic",
                "hover_effects": True,
                "fade_in_on_startup": True,
                "smooth_transitions": True
            },
            "layout": {
                "window_width": 1200,
                "window_height": 900,
                "sidebar_width": 350,
                "card_spacing": 20,
                "card_padding": 20,
                "border_radius": 16
            },
            "effects": {
                "neumorphism": True,
                "glass_morphism": True,
                "shadows": True,
                "gradients": True,
                "blur_effects": True,
                "glow_effects": True
            },
            "fonts": {
                "primary_font": "Inter",
                "secondary_font": "Noto Sans SC",
                "fallback_font": "Microsoft YaHei",
                "base_size": 11,
                "title_size": 16,
                "header_size": 20
            },
            "performance": {
                "hardware_acceleration": True,
                "smooth_scrolling": True,
                "reduce_animations": False,
                "low_power_mode": False
            },
            "accessibility": {
                "high_contrast": False,
                "large_text": False,
                "reduce_motion": False,
                "screen_reader_support": False
            },
            "presets": {
                "轻度去重": {
                    "speed_enabled": True,
                    "speed_min": 95,
                    "speed_max": 105,
                    "blur_enabled": False,
                    "transform_enabled": False,
                    "color_enabled": False
                },
                "中度去重": {
                    "speed_enabled": True,
                    "speed_min": 90,
                    "speed_max": 120,
                    "blur_enabled": True,
                    "blur_max_radius": 1.0,
                    "transform_enabled": True,
                    "transform_scale": 105,
                    "color_enabled": False
                },
                "重度去重": {
                    "speed_enabled": True,
                    "speed_min": 80,
                    "speed_max": 130,
                    "blur_enabled": True,
                    "blur_max_radius": 3.0,
                    "transform_enabled": True,
                    "transform_scale": 115,
                    "color_enabled": True
                }
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有键都存在
                    return self.merge_configs(self.default_config, config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"⚠️ 配置加载失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"⚠️ 配置保存失败: {e}")
            return False
    
    def merge_configs(self, default: Dict, user: Dict) -> Dict:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.current_config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.current_config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.current_config = self.default_config.copy()
        return self.save_config()
    
    def get_theme_colors(self):
        """获取主题颜色"""
        return self.get('theme', {})
    
    def get_animation_settings(self):
        """获取动画设置"""
        return self.get('animations', {})
    
    def get_layout_settings(self):
        """获取布局设置"""
        return self.get('layout', {})
    
    def get_effect_settings(self):
        """获取效果设置"""
        return self.get('effects', {})
    
    def get_font_settings(self):
        """获取字体设置"""
        return self.get('fonts', {})
    
    def get_preset(self, preset_name):
        """获取预设配置"""
        return self.get(f'presets.{preset_name}', {})
    
    def set_preset(self, preset_name, preset_config):
        """设置预设配置"""
        self.set(f'presets.{preset_name}', preset_config)
        return self.save_config()
    
    def apply_preset(self, preset_name):
        """应用预设配置"""
        preset = self.get_preset(preset_name)
        if preset:
            # 这里可以添加应用预设的逻辑
            print(f"✅ 应用预设: {preset_name}")
            return True
        else:
            print(f"⚠️ 预设不存在: {preset_name}")
            return False
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"⚠️ 配置导出失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self.current_config = self.merge_configs(self.default_config, imported_config)
                return self.save_config()
        except Exception as e:
            print(f"⚠️ 配置导入失败: {e}")
            return False
    
    def validate_config(self):
        """验证配置的有效性"""
        errors = []
        
        # 验证主题颜色格式
        theme = self.get('theme', {})
        color_keys = ['primary_color', 'secondary_color', 'background_color', 'surface_color']
        for key in color_keys:
            color = theme.get(key, '')
            if not color.startswith('#') or len(color) != 7:
                errors.append(f"无效的颜色格式: {key} = {color}")
        
        # 验证数值范围
        layout = self.get('layout', {})
        if layout.get('window_width', 0) < 800:
            errors.append("窗口宽度不能小于800像素")
        if layout.get('window_height', 0) < 600:
            errors.append("窗口高度不能小于600像素")
        
        # 验证动画持续时间
        animations = self.get('animations', {})
        duration = animations.get('duration', 0)
        if duration < 0 or duration > 2000:
            errors.append("动画持续时间应在0-2000毫秒之间")
        
        return errors
    
    def get_config_summary(self):
        """获取配置摘要"""
        return {
            "主题": self.get('theme.name', '未知'),
            "动画": "启用" if self.get('animations.enabled', False) else "禁用",
            "特效": "启用" if self.get('effects.neumorphism', False) else "禁用",
            "窗口大小": f"{self.get('layout.window_width', 0)}x{self.get('layout.window_height', 0)}",
            "字体": self.get('fonts.primary_font', '默认'),
            "预设数量": len(self.get('presets', {}))
        }

# 全局配置实例
ui_config = ModernUIConfig()
